<?php

namespace App\Providers;

use Illuminate\Support\Facades\Broadcast;
use Illuminate\Support\ServiceProvider;

class BroadcastServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Use cookie authentication for broadcasting routes
        Broadcast::routes(['middleware' => ['auth:web']]);

        require base_path('routes/channels.php');
    }
}
