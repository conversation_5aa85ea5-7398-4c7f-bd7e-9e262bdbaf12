import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

// Define window with Pusher property
declare global {
  interface Window {
    Pusher: typeof Pusher;
  }
}

// Make Pusher available globally
window.Pusher = Pusher;

// Initialize Laravel Echo with Reverb configuration
const echo = new Echo({
  broadcaster: 'reverb',
  key: import.meta.env.VITE_REVERB_APP_KEY || 'app-key',
  // Explicitly set the WebSocket host and port
  wsHost: import.meta.env.VITE_REVERB_HOST || 'localhost',
  wsPort: parseInt(import.meta.env.VITE_REVERB_PORT || '8080'),
  wssPort: parseInt(import.meta.env.VITE_REVERB_PORT || '8080'),
  // Disable automatic TLS unless explicitly configured
  forceTLS: (import.meta.env.VITE_REVERB_SCHEME || 'http') === 'https',
  // Disable cluster to prevent <PERSON><PERSON><PERSON> from trying to connect to Pusher.com
  cluster: '',
  // Explicitly disable encrypted connections for local development
  encrypted: false,
  // Disable stats to prevent <PERSON><PERSON><PERSON> from sending usage data
  disableStats: true,
  // Only enable WebSocket transports
  enabledTransports: ['ws'], // add wss for secure
  withCredentials: true,
  // Auth endpoint for private channels
  authEndpoint: `${import.meta.env.VITE_API_URL}/broadcasting/auth`,

});

// Add debugging to see if the token is being sent correctly
console.log('Echo Service: Auth token available:', !!localStorage.getItem('auth_token'));
console.log('Echo Service: API URL:', import.meta.env.VITE_API_URL);
console.log('Echo Service: Reverb config:', {
  key: import.meta.env.VITE_REVERB_APP_KEY,
  host: import.meta.env.VITE_REVERB_HOST,
  port: import.meta.env.VITE_REVERB_PORT,
  scheme: import.meta.env.VITE_REVERB_SCHEME
});

// Add connection debugging
if (echo.connector.pusher) {
  echo.connector.pusher.connection.bind('connected', () => {
    console.log('Echo Service: Connected to Reverb WebSocket server!');
    console.log('Echo Service: Socket ID:', echo.socketId());
  });

  echo.connector.pusher.connection.bind('disconnected', () => {
    console.log('Echo Service: Disconnected from Reverb WebSocket server');
  });

  echo.connector.pusher.connection.bind('error', (err: any) => {
    console.error('Echo Service: Connection error:', err);
  });

  echo.connector.pusher.connection.bind('state_change', (states: any) => {
    console.log('Echo Service: Connection state changed:', states);
  });
}

export default echo;
